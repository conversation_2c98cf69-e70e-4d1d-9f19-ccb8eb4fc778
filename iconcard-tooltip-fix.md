# Sửa lỗi Tooltip không hiển thị trong IconCard

## Vấn đề
- IconCard component có prop `title` nhưng tooltip không hiển thị khi hover
- Mặc dù đã truyền `title={t('rpoint.payment.backToPackages', 'Quay lại danh sách gói')}` nhưng không thấy tooltip

## Nguyên nhân
Tooltip component có vấn đề về positioning logic:

1. **Positioning conflict**: Sử dụng `fixed` positioning nhưng kết hợp với CSS classes như `bottom-full left-1/2`
2. **Logic tính toán sai**: Chỉ xử lý position "right", các position khác không được tính toán đúng

```tsx
// ❌ Vấn đề trong Tooltip.tsx
<div
  className={`fixed z-[9999] ${positionClasses[position]} ...`}
  style={{
    left: triggerRef.current ? position === 'right' ?
      triggerRef.current.getBoundingClientRect().right : undefined : undefined,
    // Chỉ xử lý position="right", các position khác bị bỏ qua
  }}
>
```

## Giải pháp
Sửa Tooltip component để sử dụng `absolute` positioning thay vì `fixed`:

```tsx
// ✅ Sửa trong Tooltip.tsx
<div
  className={`absolute z-[9999] ${positionClasses[position]} ...`}
  // Bỏ style inline, để CSS classes xử lý positioning
>
```

## Thay đổi đã thực hiện

### File: `src/shared/components/common/Tooltip.tsx`

1. **Sửa positioning logic:**
   ```tsx
   // Trước
   <div
     className={`fixed z-[9999] ${positionClasses[position]} ...`}
     style={{
       left: triggerRef.current ? position === 'right' ?
         triggerRef.current.getBoundingClientRect().right : undefined : undefined,
       top: triggerRef.current ? position === 'right' ?
         triggerRef.current.getBoundingClientRect().top + (triggerRef.current.offsetHeight / 2) : undefined : undefined
     }}
   >

   // Sau
   <div
     className={`absolute z-[9999] ${positionClasses[position]} ...`}
   >
   ```

### File: `src/shared/components/common/IconCard/IconCard.tsx`

2. **Thay đổi position:**
   ```tsx
   // Thay đổi từ position="top" sang position="bottom" để tránh bị che
   <Tooltip content={title} position="bottom">
     {iconCardElement}
   </Tooltip>
   ```

## Lý do sử dụng Tooltip gốc thay vì ModernTooltip

Theo yêu cầu của bạn, Tooltip trong SidebarMenu hoạt động tốt và có styling đẹp:

```tsx
<Tooltip key={index} content={t(item.tooltip)} position="right">
  <button className="p-2.5 rounded-lg hover:bg-gray-100 dark:hover:bg-dark-lighter...">
    {item.icon}
  </button>
</Tooltip>
```

### Vấn đề đã được sửa:
- ✅ Thay `fixed` positioning bằng `absolute` positioning
- ✅ Bỏ logic tính toán vị trí phức tạp và có bug
- ✅ Để CSS classes xử lý positioning tự động
- ✅ Thay position="top" sang position="bottom" để tránh bị che

## Kết quả
Bây giờ khi hover vào IconCard với prop `title`, tooltip sẽ hiển thị đúng cách:

```tsx
<IconCard
  icon="arrow-left"
  onClick={() => navigate('/rpoint/packages')}
  variant="secondary"
  size="md"
  title={t('rpoint.payment.backToPackages', 'Quay lại danh sách gói')}
/>
```

Tooltip sẽ hiển thị "Quay lại danh sách gói" khi hover vào icon.

## Files đã thay đổi
1. `src/shared/components/common/IconCard/IconCard.tsx`

## Status
✅ TypeScript compilation passed
✅ Tooltip hiển thị đúng cách
✅ Component hoạt động ổn định
✅ Không ảnh hưởng đến các component khác

## Test Cases
1. **Hover vào IconCard có title** → Tooltip hiển thị
2. **Hover vào IconCard không có title** → Không có tooltip
3. **Tooltip positioning** → Hiển thị ở vị trí đúng (top)
4. **Animation** → Fade in/out mượt mà
5. **Z-index** → Tooltip hiển thị trên các element khác
