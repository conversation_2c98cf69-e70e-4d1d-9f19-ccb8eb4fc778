import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  FormItem,
  Select,
  Input,
  Textarea,
} from '@/shared/components/common';
import { DigitalDeliveryDto, DigitalDeliveryMethod } from '../../types/order.types';

interface DigitalDeliveryFormProps {
  delivery?: DigitalDeliveryDto;
  onDeliveryChange: (delivery: DigitalDeliveryDto) => void;
  customerEmail?: string;
  customerPhone?: string;
}

/**
 * Form giao hàng cho sản phẩm số
 */
const DigitalDeliveryForm: React.FC<DigitalDeliveryFormProps> = ({
  delivery,
  onDeliveryChange,
  customerEmail,
  customerPhone,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Xử lý thay đổi phương thức giao hàng
  const handleMethodChange = (method: DigitalDeliveryMethod) => {
    let defaultRecipient = '';
    
    // Tự động điền recipient dựa trên phương thức
    switch (method) {
      case DigitalDeliveryMethod.EMAIL:
        defaultRecipient = customerEmail || '';
        break;
      case DigitalDeliveryMethod.SMS:
      case DigitalDeliveryMethod.ZALO:
      case DigitalDeliveryMethod.WHATSAPP:
        defaultRecipient = customerPhone || '';
        break;
      default:
        defaultRecipient = delivery?.recipient || '';
    }

    onDeliveryChange({
      ...delivery,
      method,
      recipient: defaultRecipient,
    } as DigitalDeliveryDto);
  };

  // Xử lý thay đổi recipient
  const handleRecipientChange = (recipient: string) => {
    onDeliveryChange({
      ...delivery,
      recipient,
    } as DigitalDeliveryDto);
  };

  // Xử lý thay đổi message
  const handleMessageChange = (message: string) => {
    onDeliveryChange({
      ...delivery,
      message,
    } as DigitalDeliveryDto);
  };

  // Lấy placeholder cho recipient dựa trên phương thức
  const getRecipientPlaceholder = () => {
    switch (delivery?.method) {
      case DigitalDeliveryMethod.EMAIL:
        return t('business:order.digitalDelivery.emailPlaceholder');
      case DigitalDeliveryMethod.SMS:
      case DigitalDeliveryMethod.ZALO:
      case DigitalDeliveryMethod.WHATSAPP:
        return t('business:order.digitalDelivery.phonePlaceholder');
      case DigitalDeliveryMethod.TELEGRAM:
        return t('business:order.digitalDelivery.telegramPlaceholder');
      case DigitalDeliveryMethod.DOWNLOAD_LINK:
        return t('business:order.digitalDelivery.downloadLinkPlaceholder');
      default:
        return t('business:order.digitalDelivery.recipientPlaceholder');
    }
  };

  // Lấy label cho recipient dựa trên phương thức
  const getRecipientLabel = () => {
    switch (delivery?.method) {
      case DigitalDeliveryMethod.EMAIL:
        return t('business:order.digitalDelivery.emailAddress');
      case DigitalDeliveryMethod.SMS:
      case DigitalDeliveryMethod.ZALO:
      case DigitalDeliveryMethod.WHATSAPP:
        return t('business:order.digitalDelivery.phoneNumber');
      case DigitalDeliveryMethod.TELEGRAM:
        return t('business:order.digitalDelivery.telegramUsername');
      case DigitalDeliveryMethod.DOWNLOAD_LINK:
        return t('business:order.digitalDelivery.downloadEmail');
      default:
        return t('business:order.digitalDelivery.recipient');
    }
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4">
          {t('business:order.digitalDelivery.title')}
        </Typography>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem label={t('business:order.digitalDelivery.method')} required>
            <Select
              value={delivery?.method || DigitalDeliveryMethod.EMAIL}
              onChange={(value) => handleMethodChange(value as DigitalDeliveryMethod)}
              options={[
                { 
                  value: DigitalDeliveryMethod.EMAIL, 
                  label: t('business:order.digitalDelivery.methods.email') 
                },
                { 
                  value: DigitalDeliveryMethod.SMS, 
                  label: t('business:order.digitalDelivery.methods.sms') 
                },
                { 
                  value: DigitalDeliveryMethod.ZALO, 
                  label: t('business:order.digitalDelivery.methods.zalo') 
                },
                { 
                  value: DigitalDeliveryMethod.TELEGRAM, 
                  label: t('business:order.digitalDelivery.methods.telegram') 
                },
                { 
                  value: DigitalDeliveryMethod.WHATSAPP, 
                  label: t('business:order.digitalDelivery.methods.whatsapp') 
                },
                { 
                  value: DigitalDeliveryMethod.DOWNLOAD_LINK, 
                  label: t('business:order.digitalDelivery.methods.downloadLink') 
                },
              ]}
              fullWidth
            />
          </FormItem>
          
          <FormItem label={getRecipientLabel()} required>
            <Input
              value={delivery?.recipient || ''}
              onChange={(e) => handleRecipientChange(e.target.value)}
              placeholder={getRecipientPlaceholder()}
              fullWidth
            />
          </FormItem>
        </div>
        
        <FormItem label={t('business:order.digitalDelivery.message')}>
          <Textarea
            value={delivery?.message || ''}
            onChange={(e) => handleMessageChange(e.target.value)}
            placeholder={t('business:order.digitalDelivery.messagePlaceholder')}
            rows={3}
            fullWidth
          />
        </FormItem>
      </div>
    </Card>
  );
};

export default DigitalDeliveryForm;
