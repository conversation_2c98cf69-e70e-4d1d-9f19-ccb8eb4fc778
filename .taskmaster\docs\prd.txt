# Product Requirements Document: Product Type Selection System

## Project Overview
Implement a comprehensive Product Type Selection system for the RedAI business module that allows users to create different types of products (Physical, Digital, Service, Event, Combo) with specialized forms and configurations.

## Current State
- ProductsPage.tsx currently shows ProductForm directly when "Create Product" is clicked
- ProductForm.tsx is designed for physical products with shipping configuration
- ProductTypeEnum exists but only has PHYSICAL, DIGITAL, SERVICE, EVENT (missing COMBO)
- No specialized forms for different product types

## Target State
- Product type selection interface before showing product forms
- Specialized form components for each product type
- Type-specific validation and field configurations
- Consistent UI/UX across all product forms
- Full TypeScript support with proper interfaces

## Technical Requirements

### 1. Product Type Selection Interface
- Display 5 product types: Physical, Digital, Service, Event, Combo
- Card-based selection UI with icons and descriptions
- Responsive design for mobile and desktop
- Smooth transitions and hover effects
- Cancel option to return to product list

### 2. Specialized Form Components

#### 2.1 DigitalProductForm
- Remove shipping configuration section
- Add "Digital Order Processing" section with fields:
  - Delivery method: Email, Dashboard Download, SMS, Direct Message, Zalo, Course Activation
  - Delivery timing: Immediate, Delayed
  - Access status: Delivered, Not Delivered, Delivery Error
- Add "Digital Product Output" section with fields:
  - Digital product type: Online Course, File Download, License Key, E-book
  - Download link/Key: text input
  - Usage instructions: textarea

#### 2.2 ServiceProductForm
- Remove shipping configuration section
- Add "Service Information" section with fields:
  - Service time: datetime picker + duration input
  - Service provider: text input
  - Service type: Consultation, Beauty, Maintenance, Installation
  - Service location: At Home, At Center, Online

#### 2.3 EventProductForm
- Remove shipping configuration section
- Add "Event Information" section with fields:
  - Ticket information: ticket code, QR code, seat number
  - Event time & location: datetime picker + address input
  - Attendance mode: Online/Offline with conditional Zoom link field
  - Ticket status: Available, Sold Out, Paid, Checked In
  - Attendee name: text input

#### 2.4 ComboProductForm
- Keep existing ProductForm structure
- Add section for selecting combo products
- Product selection interface with quantity and discount options

### 3. TypeScript Interfaces
- Extend ProductTypeEnum to include COMBO
- Create specialized config interfaces for each product type
- Update CreateProductDto to include productType and productConfig
- Maintain strict typing without any types

### 4. Integration Requirements
- Update ProductsPage.tsx to handle type selection flow
- Conditional rendering of appropriate form based on selected type
- Maintain existing API integration patterns
- Update component exports in index.ts

### 5. Validation & Schema
- Zod schemas for each product type with specific validations
- Conditional field requirements based on product type
- Form validation on submit with proper error handling
- Field-level validation for type-specific requirements

### 6. UI/UX Standards
- Follow existing design system components (Typography, Form, FormItem, Input, Card)
- Consistent CollapsibleCard structure across all forms
- Responsive design patterns
- Proper loading states and error handling
- Accessibility compliance

## Technical Constraints
- Must pass ESLint validation
- Strict TypeScript without any types
- Follow existing 3-layer architecture (API, Services, Hooks)
- Use system components only
- Maintain existing form patterns and validation approaches
- Support internationalization with useTranslation

## Success Criteria
- Users can select product type before creating products
- Each product type has appropriate specialized form
- All forms maintain consistent design and behavior
- Type-specific validations work correctly
- Code passes ESLint and TypeScript checks
- Responsive design works on all screen sizes
- Integration with existing API endpoints
- Proper error handling and user feedback

## Implementation Phases
1. Product Type Selection Component
2. TypeScript Interface Updates
3. Specialized Form Components Creation
4. ProductsPage Integration
5. Component Export Updates
6. Validation Schema Implementation
7. UI/UX Polish and Testing
8. Final Integration and Quality Assurance
