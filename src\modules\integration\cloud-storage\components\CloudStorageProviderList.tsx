import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  Icon,
  EmptyState,
  ResponsiveGrid,
  Badge,
  Modal,
  ActionMenu,
} from '@/shared/components/common';
import { CloudStorageProviderConfiguration } from '../types';
import { useCloudStorageProviders, useDeleteCloudStorageProvider, useTestCloudStorageProvider } from '../hooks';
import { CLOUD_STORAGE_PROVIDER_TYPES } from '../constants';

interface CloudStorageProviderListProps {
  onCreateNew?: () => void;
  onEdit?: (provider: CloudStorageProviderConfiguration) => void;
}

/**
 * Component hiển thị danh sách Cloud Storage Providers
 */
const CloudStorageProviderList: React.FC<CloudStorageProviderListProps> = ({
  onCreateNew,
  onEdit,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  
  // State
  const [deletingProvider, setDeletingProvider] = useState<CloudStorageProviderConfiguration | null>(null);
  const [testingProvider, setTestingProvider] = useState<CloudStorageProviderConfiguration | null>(null);

  // Hooks
  const { data: providersResponse, isLoading } = useCloudStorageProviders();
  const deleteProviderMutation = useDeleteCloudStorageProvider();
  const testProviderMutation = useTestCloudStorageProvider();

  const providers = (providersResponse?.result as unknown as CloudStorageProviderConfiguration[]) || [];

  // Handlers
  const handleEdit = (provider: CloudStorageProviderConfiguration) => {
    onEdit?.(provider);
  };

  const handleDeleteClick = (provider: CloudStorageProviderConfiguration) => {
    setDeletingProvider(provider);
  };

  const handleDeleteConfirm = async () => {
    if (!deletingProvider) return;
    
    try {
      await deleteProviderMutation.mutateAsync(deletingProvider.id);
      setDeletingProvider(null);
    } catch (error) {
      console.error('Delete provider error:', error);
    }
  };

  const handleTestConnection = async (provider: CloudStorageProviderConfiguration) => {
    setTestingProvider(provider);
    try {
      await testProviderMutation.mutateAsync({ id: provider.id });
      // TODO: Show success toast
    } catch (error) {
      console.error('Test connection error:', error);
      // TODO: Show error toast
    } finally {
      setTestingProvider(null);
    }
  };

  // Render provider card
  const renderProviderCard = (provider: CloudStorageProviderConfiguration) => {
    const providerInfo = CLOUD_STORAGE_PROVIDER_TYPES[provider.providerType];
    
    return (
      <Card key={provider.id} className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <Icon name="cloud" size="lg" className="text-primary" />
            <div>
              <Typography variant="h6" className="font-semibold">
                {provider.providerName}
              </Typography>
              <Typography variant="caption" className="text-muted-foreground">
                {providerInfo?.displayName || provider.providerType}
              </Typography>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant={provider.isActive ? 'success' : 'secondary'}>
              {provider.isActive ? t('common:active') : t('common:inactive')}
            </Badge>
            
            <ActionMenu
              items={[
                {
                  id: 'edit',
                  label: t('common:edit'),
                  icon: 'edit',
                  onClick: () => handleEdit(provider),
                },
                {
                  id: 'test',
                  label: t('integration:cloudStorage.testConnection'),
                  icon: 'zap',
                  onClick: () => handleTestConnection(provider),
                  disabled: testingProvider?.id === provider.id,
                },
                {
                  id: 'delete',
                  label: t('common:delete'),
                  icon: 'trash',
                  onClick: () => handleDeleteClick(provider),
                },
              ]}
            />
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Icon name="folder" size="sm" className="text-muted-foreground" />
            <Typography variant="caption" className="text-muted-foreground">
              {provider.rootFolderId || t('integration:cloudStorage.rootFolder')}
            </Typography>
          </div>
          
          {provider.autoSync && (
            <div className="flex items-center gap-2">
              <Icon name="refresh-cw" size="sm" className="text-primary" />
              <Typography variant="caption" className="text-primary">
                {t('integration:cloudStorage.autoSyncEnabled')}
              </Typography>
            </div>
          )}
          
          {provider.lastSyncAt && (
            <div className="flex items-center gap-2">
              <Icon name="clock" size="sm" className="text-muted-foreground" />
              <Typography variant="caption" className="text-muted-foreground">
                {t('integration:cloudStorage.lastSync')}: {new Date(provider.lastSyncAt).toLocaleString()}
              </Typography>
            </div>
          )}
        </div>
      </Card>
    );
  };

  return (
    <div className="w-full space-y-6">
      {/* Header */}
      <Card>
        <div className="p-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div>
              <Typography variant="h3" className="mb-2">
                {t('integration:cloudStorage.title')}
              </Typography>
              <Typography variant="body1" className="text-muted-foreground">
                {t('integration:cloudStorage.description')}
              </Typography>
            </div>

            <Button
              variant="primary"
              onClick={onCreateNew}
              leftIcon={<Icon name="plus" size="sm" />}
            >
              {t('integration:cloudStorage.addProvider')}
            </Button>
          </div>
        </div>
      </Card>

      {/* Content */}
      {isLoading ? (
        <Card>
          <div className="p-8 text-center">
            <Icon name="loading" size="lg" className="animate-spin text-primary mb-4" />
            <Typography variant="body1" className="text-muted-foreground">
              {t('common:loading')}
            </Typography>
          </div>
        </Card>
      ) : providers.length === 0 ? (
        <Card>
          <div className="p-8">
            <EmptyState
              icon="cloud"
              title={t('integration:cloudStorage.empty.title')}
              description={t('integration:cloudStorage.empty.description')}
              actions={
                <Button
                  variant="primary"
                  onClick={onCreateNew}
                  leftIcon={<Icon name="plus" size="sm" />}
                >
                  {t('integration:cloudStorage.addFirstProvider')}
                </Button>
              }
            />
          </div>
        </Card>
      ) : (
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 3 }}
          gap={6}
        >
          {providers.map(renderProviderCard)}
        </ResponsiveGrid>
      )}

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={!!deletingProvider}
        onClose={() => setDeletingProvider(null)}
        title={t('integration:cloudStorage.deleteProvider')}
      >
        <div className="space-y-4">
          <Typography variant="body1">
            {t('integration:cloudStorage.deleteConfirmation', {
              name: deletingProvider?.providerName,
            })}
          </Typography>
          
          <div className="flex gap-3 justify-end">
            <Button
              variant="ghost"
              onClick={() => setDeletingProvider(null)}
            >
              {t('common:cancel')}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
              disabled={deleteProviderMutation.isPending}
            >
              {deleteProviderMutation.isPending ? (
                <>
                  <Icon name="loading" size="sm" className="mr-2 animate-spin" />
                  {t('common:deleting')}
                </>
              ) : (
                t('common:delete')
              )}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default CloudStorageProviderList;
