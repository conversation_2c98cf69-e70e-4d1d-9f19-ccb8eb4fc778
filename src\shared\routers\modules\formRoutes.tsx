import { Loading } from '@/shared/components/common';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

// Lazy load MainLayout để tránh import vòng tròn
import MainLayout from '@/shared/layouts/MainLayout';

// Import Form pages
const FormPage = lazy(() => import('@/modules/components/pages/form/index.tsx'));
const BasicFormPage = lazy(() => import('@/modules/components/pages/form/BasicFormPage'));
const FormLayoutPage = lazy(() => import('@/modules/components/pages/form/FormLayoutPage'));
const ConditionalFieldsPage = lazy(
  () => import('@/modules/components/pages/form/ConditionalFieldsPage')
);
const ApiIntegrationPage = lazy(() => import('@/modules/components/pages/form/ApiIntegrationPage'));
const FormDependenciesPage = lazy(() => import('@/modules/components/pages/FormDependenciesPage'));
const FormSectionsPage = lazy(() => import('@/modules/components/pages/FormSectionsPage.tsx'));
const FormTemplatesPage = lazy(() => import('@/modules/components/pages/form/FormTemplatesPage'));

// Import Form demo pages
const FormConditionalDemo = lazy(
  () => import('@/modules/components/pages/form/FormConditionalDemo')
);
const FormLayoutDemo = lazy(() => import('@/modules/components/pages/form/FormLayoutDemo'));
const FormDependencyDemo = lazy(() => import('@/modules/components/pages/form/FormDependencyDemo'));
const FormArrayDemo = lazy(() => import('@/modules/components/pages/form/FormArrayDemo'));
const FormArrayDemoPage = lazy(() => import('@/modules/components/pages/form/FormArrayDemoPage'));
const SelectDemo = lazy(() => import('@/modules/components/pages/form/SelectDemo'));
const SelectDemoNew = lazy(() => import('@/modules/components/pages/form/SelectDemoNew'));
const AdvancedSelectDemo = lazy(() => import('@/modules/components/pages/form/AdvancedSelectDemo'));
const AdvancedSelectDemoNew = lazy(
  () => import('@/modules/components/pages/form/AdvancedSelectDemoNew')
);
const SelectUsageDemo = lazy(() => import('@/modules/components/pages/form/SelectUsageDemo'));
const DatePickerDemo = lazy(() => import('@/modules/components/pages/form/DatePickerDemo'));
const DatePickerAdvancedDemo = lazy(
  () => import('@/modules/components/pages/form/DatePickerAdvancedDemo')
);
const CheckboxRadioDemo = lazy(() => import('@/modules/components/pages/form/CheckboxRadioDemo'));
const DoubleDatePickerDemo = lazy(() => import('@/modules/components/pages/form/DoubleDatePickerDemo'));

/**
 * Form module routes
 */
const formRoutes: RouteObject[] = [
  // Form component routes
  {
    path: '/components/form',
    element: (
      <Suspense fallback={<Loading />}>
        <MainLayout title="Form Components">
          <Suspense fallback={<Loading />}>
            <FormPage />
          </Suspense>
        </MainLayout>
      </Suspense>
    ),
  },
  {
    path: '/components/form/basic',
    element: (
      <MainLayout title="Basic Form Components">
        <Suspense fallback={<Loading />}>
          <BasicFormPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/layouts',
    element: (
      <MainLayout title="Form Layouts">
        <Suspense fallback={<Loading />}>
          <FormLayoutPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/conditional',
    element: (
      <MainLayout title="Conditional Form Fields">
        <Suspense fallback={<Loading />}>
          <ConditionalFieldsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/sections',
    element: (
      <MainLayout title="Form Sections">
        <Suspense fallback={<Loading />}>
          <FormSectionsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/dependencies',
    element: (
      <MainLayout title="Form Field Dependencies">
        <Suspense fallback={<Loading />}>
          <FormDependenciesPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/api-integration',
    element: (
      <MainLayout title="API Form Integration">
        <Suspense fallback={<Loading />}>
          <ApiIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form/templates',
    element: (
      <MainLayout title="Form Templates">
        <Suspense fallback={<Loading />}>
          <FormTemplatesPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Form demo routes
  {
    path: '/form-conditional-demo',
    element: (
      <MainLayout title="Form Conditional Demo">
        <Suspense fallback={<Loading />}>
          <FormConditionalDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/form-layout-demo',
    element: (
      <MainLayout title="Form Layout Demo">
        <Suspense fallback={<Loading />}>
          <FormLayoutDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/form-dependency-demo',
    element: (
      <MainLayout title="Form Dependency Demo">
        <Suspense fallback={<Loading />}>
          <FormDependencyDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/form-array-demo',
    element: (
      <MainLayout title="Form Array Demo">
        <Suspense fallback={<Loading />}>
          <FormArrayDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/components/form-array',
    element: (
      <MainLayout title="Form Array">
        <Suspense fallback={<Loading />}>
          <FormArrayDemoPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/select-demo',
    element: (
      <MainLayout title="Select Component Demo">
        <Suspense fallback={<Loading />}>
          <SelectDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/select-demo-new',
    element: (
      <MainLayout title="Select Component Demo (New)">
        <Suspense fallback={<Loading />}>
          <SelectDemoNew />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/advanced-select-demo',
    element: (
      <MainLayout title="Advanced Select Components">
        <Suspense fallback={<Loading />}>
          <AdvancedSelectDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/advanced-select-demo-new',
    element: (
      <MainLayout title="Advanced Select Components (New)">
        <Suspense fallback={<Loading />}>
          <AdvancedSelectDemoNew />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/select-usage-demo',
    element: (
      <MainLayout title="Select Usage Examples">
        <Suspense fallback={<Loading />}>
          <SelectUsageDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/datepicker-demo',
    element: (
      <MainLayout title="DatePicker Component">
        <Suspense fallback={<Loading />}>
          <DatePickerDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/datepicker-advanced-demo',
    element: (
      <MainLayout title="DatePicker Advanced Examples">
        <Suspense fallback={<Loading />}>
          <DatePickerAdvancedDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/checkbox-radio-demo',
    element: (
      <MainLayout title="Checkbox & Radio Components">
        <Suspense fallback={<Loading />}>
          <CheckboxRadioDemo />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/double-datepicker-demo',
    element: (
      <MainLayout title="DoubleDatePicker Component">
        <Suspense fallback={<Loading />}>
          <DoubleDatePickerDemo />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default formRoutes;
