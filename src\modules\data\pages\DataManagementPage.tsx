import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { FileImage, FileText, Link, Database } from 'lucide-react';

import { ModuleCard } from '@/modules/components/card';
import { Typography } from '@/shared/components/common';
import { ResponsiveGrid } from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import type { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';
import { useDataOverview, useStorageInfo } from '../hooks/useDataOverview';
import StorageSlider from '../components/StorageSlider';
import StorageUsageCard from '../components/StorageUsageCard';

/**
 * Trang tổng quan quản lý dữ liệu
 */
const DataManagementPage: React.FC = () => {
  const { t } = useTranslation(['data']);

  // Fetch data overview từ API
  const { data: overviewData, isLoading: isOverviewLoading } = useDataOverview();
  const { data: storageData } = useStorageInfo();

  // State cho việc hiển thị StorageSlider
  const [showStorageSlider, setShowStorageSlider] = useState(false);

  // Overview statistics với data từ API hoặc fallback
  const overviewStats: OverviewCardProps[] = useMemo(() => [
    {
      title: t('data:overview.stats.totalMedia', 'Tổng Media Files'),
      value: overviewData?.totalMediaFiles || 1250,
      description: t('data:overview.stats.mediaDescription', '+15 files mới'),
      icon: FileImage,
      color: 'blue',
      isLoading: isOverviewLoading,
    },
    {
      title: t('data:overview.stats.totalKnowledge', 'File Tri Thức'),
      value: overviewData?.totalKnowledgeFiles || 340,
      description: t('data:overview.stats.knowledgeDescription', '+8 files mới'),
      icon: FileText,
      color: 'green',
      isLoading: isOverviewLoading,
    },
    {
      title: t('data:overview.stats.totalUrls', 'Tổng URLs'),
      value: overviewData?.totalUrls || 89,
      description: t('data:overview.stats.urlDescription', '+3 URLs mới'),
      icon: Link,
      color: 'orange',
      isLoading: isOverviewLoading,
    },
    {
      title: t('data:overview.stats.totalVectorStores', 'Vector Stores'),
      value: overviewData?.totalVectorStores || 12,
      description: overviewData?.storageUsed || t('data:overview.stats.vectorDescription', '2.4 GB sử dụng'),
      icon: Database,
      color: 'purple',
      isLoading: isOverviewLoading,
    },
  ], [t, overviewData, isOverviewLoading]);

  // Handle show storage upgrade
  const handleShowStorageUpgrade = () => {
    setShowStorageSlider(true);
  };

  // Handle close storage slider
  const handleCloseStorageSlider = () => {
    setShowStorageSlider(false);
  };

  // Handle purchase storage - gọi API thanh toán trực tiếp
  const handlePurchaseStorage = async (storageAmount: number, price: number) => {
    try {
      // TODO: Implement actual payment API call
      console.log('Processing payment:', { storageAmount, price });

      // Giả lập API call thanh toán
      // const response = await paymentAPI.purchaseStorage({ storageAmount, price });

      // Hiển thị thông báo thành công
      // NotificationUtil.success({
      //   message: t('data:storage.purchaseSuccess', 'Nâng cấp dung lượng thành công!'),
      //   duration: 3000,
      // });

      // Đóng slider sau khi thanh toán thành công
      setShowStorageSlider(false);

      // Refresh storage data
      // refetch storage info

    } catch (error) {
      console.error('Payment error:', error);
      // NotificationUtil.error({
      //   message: t('data:storage.purchaseError', 'Có lỗi xảy ra khi thanh toán. Vui lòng thử lại.'),
      //   duration: 3000,
      // });
    }
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Overview Statistics */}
      <ListOverviewCard
        items={overviewStats}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
        gap={4}
        isLoading={isOverviewLoading}
        skeletonCount={4}
      />

      {/* Storage Management */}
      <StorageUsageCard
        usedFormatted={storageData?.usedFormatted || '2.4 GB'}
        totalFormatted={storageData?.totalFormatted || '10 GB'}
        percentage={storageData?.percentage || 24}
        onUpgrade={handleShowStorageUpgrade}
      />

      {/* Storage Slider - chỉ hiện khi bấm nút nâng cấp */}
      {showStorageSlider && (
        <StorageSlider
          currentStorage={storageData?.total || 10}
          onPurchase={handlePurchaseStorage}
          onClose={handleCloseStorageSlider}
        />
      )}

      {/* Module Navigation */}
      <div>
        <Typography variant="h5" className="mb-4 font-semibold">
          {t('data:modules.title', 'Quản Lý Dữ Liệu')}
        </Typography>
        <ResponsiveGrid
          maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
          maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
          gap={6}
        >
          {/* Media Card */}
          <ModuleCard
            title={t('data:media.title', 'Thư viện Media')}
            description={t(
              'data:media.description',
              'Quản lý các tệp tin media như hình ảnh, video, âm thanh và tài liệu.'
            )}
            icon="file-media"
            linkTo="/data/media"
          />

          {/* Knowledge Files Card */}
          <ModuleCard
            title={t('data:knowledgeFiles.title', 'File tri thức')}
            description={t(
              'data:knowledgeFiles.description',
              'Quản lý các tệp tin tri thức được sử dụng cho AI và vector store.'
            )}
            icon="file-text"
            linkTo="/data/knowledge-files"
          />

          {/* URL Card */}
          <ModuleCard
            title={t('data:url.title', 'Quản lý URL')}
            description={t(
              'data:url.description',
              'Quản lý các URL và tài nguyên web được sử dụng trong hệ thống.'
            )}
            icon="link"
            linkTo="/data/url"
          />

          {/* Vector Store Card */}
          <ModuleCard
            title={t('data:vectorStore.title', 'Vector Store')}
            description={t(
              'data:vectorStore.description',
              'Quản lý các vector store và embedding cho các ứng dụng AI và tìm kiếm ngữ nghĩa.'
            )}
            icon="server"
            linkTo="/data/vector-store"
          />
        </ResponsiveGrid>
      </div>
    </div>
  );
};

export default DataManagementPage;
