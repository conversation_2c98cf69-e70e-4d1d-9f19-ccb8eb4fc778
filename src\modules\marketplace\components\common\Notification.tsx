import { useTranslation } from 'react-i18next';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';

/**
 * Interface cho notification options
 */
export interface NotificationOptions {
  title?: string;
  message: string;
  duration?: number;
}

/**
 * Hook để hiển thị notification với đa ngôn ngữ
 */
export const useMarketplaceNotification = () => {
  const { t } = useTranslation('marketplace');
  const { success, error: showError, warning, info } = useSmartNotification();

  return {
    /**
     * Hiển thị thông báo thành công
     */
    success: (options: NotificationOptions) => {
      success({
        title: options.title || t('marketplace:notification.success', 'Thành công'),
        message: options.message,
        duration: options.duration,
      });
    },

    /**
     * Hiển thị thông báo lỗi
     */
    error: (options: NotificationOptions) => {
      showError({
        title: options.title || t('marketplace:notification.error', 'Lỗi'),
        message: options.message,
        duration: options.duration,
      });
    },

    /**
     * Hiển thị thông báo cảnh báo
     */
    warning: (options: NotificationOptions) => {
      warning({
        title: options.title || t('marketplace:notification.warning', 'Cảnh báo'),
        message: options.message,
        duration: options.duration,
      });
    },

    /**
     * Hiển thị thông báo thông tin
     */
    info: (options: NotificationOptions) => {
      info({
        title: options.title || t('marketplace:notification.info', 'Thông tin'),
        message: options.message,
        duration: options.duration,
      });
    },

    // Các thông báo cụ thể cho marketplace
    product: {
      createSuccess: () => {
        success({
          title: t('marketplace:notification.success', 'Thành công'),
          message: t('marketplace:notification.product.createSuccess', 'Sản phẩm  đã được tạo thành công!'),
        });
      },

      createError: (error?: string) => {
        showError({
          title: t('marketplace:notification.error', 'Lỗi'),
          message: t('marketplace:notification.product.createError', 'Không thể tạo sản phẩm. {{error}}', { error: error || t('marketplace:notification.tryAgain', 'Vui lòng thử lại.') }),
        });
      },

      updateSuccess: (productName: string) => {
        success({
          title: t('marketplace:notification.success', 'Thành công'),
          message: t('marketplace:notification.product.updateSuccess', 'Sản phẩm "{{name}}" đã được cập nhật thành công!', { name: productName }),
        });
      },

      updateError: (error?: string) => {
        showError({
          title: t('marketplace:notification.error', 'Lỗi'),
          message: t('marketplace:notification.product.updateError', 'Không thể cập nhật sản phẩm. {{error}}', { error: error || t('marketplace:notification.tryAgain', 'Vui lòng thử lại.') }),
        });
      },

      deleteSuccess: (productName?: string) => {
        success({
          title: t('marketplace:notification.success', 'Thành công'),
          message: productName
            ? t('marketplace:notification.product.deleteSuccess', 'Sản phẩm "{{name}}" đã được xóa thành công!', { name: productName })
            : t('marketplace:notification.product.deleteSuccessGeneric', 'Sản phẩm đã được xóa thành công!'),
        });
      },

      deleteError: (error?: string) => {
        showError({
          title: t('marketplace:notification.error', 'Lỗi'),
          message: t('marketplace:notification.product.deleteError', 'Không thể xóa sản phẩm. {{error}}', { error: error || t('marketplace:notification.tryAgain', 'Vui lòng thử lại.') }),
        });
      },

      batchDeleteSuccess: (count: number) => {
        success({
          title: t('marketplace:notification.success', 'Thành công'),
          message: t('marketplace:notification.product.batchDeleteSuccess', 'Đã xóa thành công {{count}} sản phẩm!', { count }),
        });
      },

      batchDeleteError: (error?: string) => {
        showError({
          title: t('marketplace:notification.error', 'Lỗi'),
          message: t('marketplace:notification.product.batchDeleteError', 'Không thể xóa sản phẩm. {{error}}', { error: error || t('marketplace:notification.tryAgain', 'Vui lòng thử lại.') }),
        });
      },

      submitForApprovalSuccess: (productName: string) => {
        success({
          title: t('marketplace:notification.success', 'Thành công'),
          message: t('marketplace:notification.product.submitForApprovalSuccess', 'Sản phẩm "{{name}}" đã được gửi để duyệt!', { name: productName }),
        });
      },

      submitForApprovalError: (error?: string) => {
        showError({
          title: t('marketplace:notification.error', 'Lỗi'),
          message: t('marketplace:notification.product.submitForApprovalError', 'Không thể gửi sản phẩm để duyệt. {{error}}', { error: error || t('marketplace:notification.tryAgain', 'Vui lòng thử lại.') }),
        });
      },

      cancelSubmissionSuccess: (productName: string) => {
        success({
          title: t('marketplace:notification.success', 'Thành công'),
          message: t('marketplace:notification.product.cancelSubmissionSuccess', 'Đã hủy gửi duyệt sản phẩm "{{name}}"!', { name: productName }),
        });
      },

      cancelSubmissionError: (error?: string) => {
        showError({
          title: t('marketplace:notification.error', 'Lỗi'),
          message: t('marketplace:notification.product.cancelSubmissionError', 'Không thể hủy gửi duyệt. {{error}}', { error: error || t('marketplace:notification.tryAgain', 'Vui lòng thử lại.') }),
        });
      },
    },
  };
};

export default useMarketplaceNotification;
