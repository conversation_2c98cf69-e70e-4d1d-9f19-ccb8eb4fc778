/**
 * Component hiển thị QR Code thanh toán
 */
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Button, Icon } from '@/shared/components/common';
import { formatVND } from '../utils/rpoint.utils';
import PaymentStatusLoading from './PaymentStatusLoading';

interface QRCodePaymentProps {
  /**
   * URL QR Code
   */
  qrCodeUrl: string;

  /**
   * Số tiền thanh toán
   */
  amount: number;

  /**
   * Nội dung chuyển khoản
   */
  transferContent: string;

  /**
   * Thông tin ngân hàng
   */
  bankInfo: {
    name: string;
    accountNumber: string;
    accountHolder: string;
    logo?: string;
  };

  /**
   * Trạng thái thanh toán
   */
  status?: 'PENDING' | 'COMPLETED';

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị QR Code thanh toán
 */
const QRCodePayment: React.FC<QRCodePaymentProps> = ({
  qrCodeUrl,
  amount,
  transferContent,
  bankInfo,
  status = 'PENDING',
  className = '',
}) => {
  const { t } = useTranslation(['rpoint']);

  // Xử lý sao chép nội dung chuyển khoản
  const handleCopyTransferContent = () => {
    navigator.clipboard.writeText(transferContent);
  };

  // Xử lý sao chép số tài khoản
  const handleCopyAccountNumber = () => {
    navigator.clipboard.writeText(bankInfo.accountNumber);
  };

  return (
    <Card
      title={t('rpoint.order.payment.title', 'Phương thức thanh toán')}
      className={className}
      variant="bordered"
    >
      <div className="flex flex-col md:flex-row gap-6">
        {/* QR Code */}
        <div className="flex-1 flex flex-col items-center">
          <Typography variant="subtitle2" className="mb-2 text-center">
            {t('rpoint.order.payment.scanQR', 'Quét mã QR để thanh toán')}
          </Typography>
          <div className="border border-border p-2 rounded-lg mb-4">
            <img src={qrCodeUrl} alt="QR Code" className="w-full max-w-[200px] h-auto" />
          </div>

          {/* Trạng thái thanh toán */}
          <div className="flex justify-center">
            {status === 'PENDING' ? (
              <PaymentStatusLoading text={t('rpoint.payment.statusPending', 'Chưa hoàn tất')} />
            ) : (
              <Typography variant="body2" className="font-semibold text-success">
                {t('rpoint.payment.statusCompleted', 'Đã thanh toán')}
              </Typography>
            )}
          </div>
        </div>

        {/* Thông tin chuyển khoản */}
        <div className="flex-1">
          <Typography variant="subtitle2" className="mb-3">
            {t('rpoint.order.payment.bankTransfer', 'Thông tin chuyển khoản')}
          </Typography>

          <div className="space-y-4">
            {/* Số tiền */}
            <div>
              <Typography variant="body2" color="muted" className="mb-1">
                {t('rpoint.order.payment.amount', 'Số tiền')}
              </Typography>
              <Typography variant="body2" className="font-semibold text-lg">
                {formatVND(amount)}
              </Typography>
            </div>

            {/* Thông tin ngân hàng */}
            <div className="flex items-center space-x-3">
              {bankInfo.logo && (
                <img
                  src={bankInfo.logo}
                  alt={bankInfo.name}
                  className="w-8 h-8 object-contain flex-shrink-0"
                />
              )}
              <div>
                <Typography variant="body2" className="font-medium">
                  {bankInfo.name}
                </Typography>
              </div>
            </div>

            {/* Chủ tài khoản */}
            <div>
              <Typography variant="body2" color="muted" className="mb-1">
                {t('rpoint.order.payment.accountHolder', 'Chủ tài khoản')}
              </Typography>
              <Typography variant="body2" className="font-medium">
                {bankInfo.accountHolder}
              </Typography>
            </div>

            {/* Số tài khoản */}
            <div>
              <Typography variant="body2" color="muted" className="mb-1">
                {t('rpoint.order.payment.accountNumber', 'Số tài khoản')}
              </Typography>
              <div className="flex items-center space-x-2">
                <Typography variant="body2" className="font-medium">
                  {bankInfo.accountNumber}
                </Typography>
                <Button variant="ghost" size="sm" onClick={handleCopyAccountNumber} className="p-1">
                  <Icon name="copy" size="sm" />
                </Button>
              </div>
            </div>

            {/* Nội dung chuyển khoản */}
            <div>
              <Typography variant="body2" color="muted" className="mb-1">
                {t('rpoint.order.payment.transferContent', 'Nội dung chuyển khoản')}
              </Typography>
              <div className="flex items-center space-x-2">
                <Typography variant="body2" className="font-medium">
                  {transferContent}
                </Typography>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyTransferContent}
                  className="p-1"
                >
                  <Icon name="copy" size="sm" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default QRCodePayment;
