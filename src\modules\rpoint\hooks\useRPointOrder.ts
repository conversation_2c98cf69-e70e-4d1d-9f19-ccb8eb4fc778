/**
 * Hook quản lý state và logic cho trang đơn hàng
 */
import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { RPointOrderService, CouponResponseDto } from '../services/order.service';
import { InvoiceType, OrderFormValues } from '../schemas/order.schema';
import useChatNotification from '@/shared/hooks/common/useChatNotification';

// Định nghĩa kiểu dữ liệu cho lỗi API
interface ApiError {
  message: string;
  code?: number;
  [key: string]: unknown;
}

interface UseRPointOrderProps {
  /**
   * ID của gói R-Point
   */
  packageId: number;

  /**
   * Số lượng R-Point
   */
  points: number;

  /**
   * Giá tiền
   */
  price: number;

  /**
   * Tên gói
   */
  packageName: string;
}

/**
 * Hook quản lý state và logic cho trang đơn hàng
 */
export const useRPointOrder = ({ packageId, points }: UseRPointOrderProps) => {
  const navigate = useNavigate();
  const { showNotification } = useChatNotification();

  // State cho mã khuyến mãi
  const [couponCode, setCouponCode] = useState<string | null>(null);
  const [discount, setDiscount] = useState<number>(0);
  const [randomCoupons, setRandomCoupons] = useState<CouponResponseDto[]>([]);
  const [isLoadingCoupons, setIsLoadingCoupons] = useState<boolean>(false);

  // State cho đơn hàng đã tạo
  const [createdOrder, setCreatedOrder] = useState<Record<string, unknown> | null>(null);

  // Giá trị mặc định cho form
  const defaultValues: OrderFormValues = {
    couponCode: '',
    invoiceInfo: {
      type: InvoiceType.PERSONAL,
      fullName: '',
      phoneNumber: '',
      address: '',
      email: '',
      dateOfBirth: undefined,
      gender: undefined,
    },
  };

  // Mutation áp dụng mã khuyến mãi
  const applyCouponMutation = useMutation({
    mutationFn: (code: string) => {
      return RPointOrderService.applyCoupon({
        pointPackageId: packageId,
        pointAmount: points,
        couponCode: code,
      });
    },
    onSuccess: data => {
      setCouponCode(data.couponCode);
      setDiscount(data.discountAmount);
      showNotification(
        'success',
        `Áp dụng mã khuyến mãi thành công: Bạn được giảm ${data.discountAmount.toLocaleString('vi-VN')} VND`
      );
    },
    onError: (error: ApiError) => {
      showNotification(
        'error',
        `Áp dụng mã khuyến mãi thất bại: ${error.message || 'Mã khuyến mãi không hợp lệ hoặc đã hết hạn'}`
      );
    },
  });

  // Mutation tạo đơn hàng
  const createOrderMutation = useMutation({
    mutationFn: (values: OrderFormValues) => {
      return RPointOrderService.createOrder({
        pointPackageId: packageId,
        pointAmount: points,
        couponCode: values.couponCode || undefined,
        invoiceInfo:
          values.invoiceInfo.type === InvoiceType.BUSINESS ? values.invoiceInfo : undefined,
      });
    },
    onSuccess: data => {
      setCreatedOrder(data as unknown as Record<string, unknown>);
      showNotification(
        'success',
        'Tạo đơn hàng thành công: Vui lòng thanh toán để hoàn tất giao dịch'
      );
      // Chuyển hướng đến trang thanh toán
      navigate(`/rpoint/payment/${data.id}`);
    },
    onError: (error: ApiError) => {
      showNotification(
        'error',
        `Tạo đơn hàng thất bại: ${error.message || 'Đã có lỗi xảy ra, vui lòng thử lại sau'}`
      );
    },
  });

  // Xử lý khi áp dụng mã khuyến mãi
  const handleApplyCoupon = (code: string) => {
    if (code) {
      applyCouponMutation.mutate(code);
    }
  };

  // Xử lý khi submit form
  const handleSubmit = (values: OrderFormValues) => {
    createOrderMutation.mutate(values);
  };

  // Hàm lấy danh sách mã khuyến mãi ngẫu nhiên
  const fetchRandomCoupons = useCallback(async () => {
    if (!packageId || !points) return;

    try {
      setIsLoadingCoupons(true);
      const coupons = await RPointOrderService.getRandomCoupons({
        pointId: packageId,
        pointAmount: points,
      });
      setRandomCoupons(coupons);
    } catch (error) {
      console.error('Error fetching random coupons:', error);
    } finally {
      setIsLoadingCoupons(false);
    }
  }, [packageId, points, setIsLoadingCoupons, setRandomCoupons]);

  // Gọi API lấy mã khuyến mãi ngẫu nhiên khi component được tải
  useEffect(() => {
    fetchRandomCoupons();
  }, [packageId, points, fetchRandomCoupons]);

  // Lấy danh sách mã khuyến mãi gợi ý (API cũ - giữ lại để tương thích)
  const { data: suggestedCoupons = [] } = useMutation({
    mutationFn: () => RPointOrderService.getSuggestedCoupons(),
  });

  // Chuyển đổi danh sách mã khuyến mãi ngẫu nhiên sang định dạng cũ để tương thích
  const formattedRandomCoupons = randomCoupons.map(coupon => ({
    code: coupon.code,
    description: coupon.description,
  }));

  return {
    defaultValues,
    couponCode,
    discount,
    suggestedCoupons: formattedRandomCoupons.length > 0 ? formattedRandomCoupons : suggestedCoupons,
    randomCoupons, // Thêm danh sách mã khuyến mãi ngẫu nhiên với đầy đủ thông tin
    isApplyingCoupon: applyCouponMutation.isPending,
    isCreatingOrder: createOrderMutation.isPending,
    isLoadingCoupons,
    handleApplyCoupon,
    handleSubmit,
    createdOrder,
  };
};
