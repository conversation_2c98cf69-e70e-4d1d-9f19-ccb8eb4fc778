/**
 * Trang thanh toán sau khi tạo đơn hàng R-Point
 */
import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Typography, Button, Card, Icon, IconCard } from '@/shared/components/common';
import QRCodePayment from '../components/QRCodePayment';
import { RPointOrderService } from '../services/order.service';
import { useQuery } from '@tanstack/react-query';
import { useUserPoints } from '@/modules/profile/hooks/useUser';

/**
 * Trang thanh toán sau khi tạo đơn hàng R-Point
 */
const RPointPaymentPage: React.FC = () => {
  const { t } = useTranslation(['rpoint']);
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // State để theo dõi trạng thái thanh toán
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  // Hook để lấy và cập nhật số point của người dùng
  const { refetch: refetchUserPoints } = useUserPoints();

  // Sử dụng React Query để lấy thông tin đơn hàng với caching
  const {
    data: orderInfo,
    isLoading,
    error
  } = useQuery({
    queryKey: ['payment-info', id],
    queryFn: () => RPointOrderService.getPaymentInfo(id as string),
    enabled: !!id,
    staleTime: 30 * 60 * 1000, // 30 phút - dữ liệu không thay đổi trong thời gian dài
    gcTime: 60 * 60 * 1000, // 1 giờ - giữ trong cache lâu hơn
    retry: 1, // Chỉ thử lại 1 lần nếu có lỗi
  });

  // Hàm kiểm tra trạng thái giao dịch
  const checkTransactionStatus = useCallback(async () => {
    if (!id || paymentSuccess) return;

    try {
      // Không cần set isCheckingStatus = true ở đây vì chúng ta muốn hiển thị Loading liên tục
      const status = await RPointOrderService.checkTransactionStatus(id);

      // Nếu giao dịch đã hoàn thành
      if (status && status.status === 'COMPLETED') {
        setPaymentSuccess(true);
        // Cập nhật số point của người dùng
        await refetchUserPoints();
        // Dừng việc kiểm tra trạng thái
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error checking transaction status:', error);
      return false;
    }
    // Không cần finally và setIsCheckingStatus(false) vì chúng ta muốn hiển thị Loading liên tục
  }, [id, paymentSuccess, refetchUserPoints]);

  // Thiết lập interval để kiểm tra trạng thái giao dịch mỗi 2 giây
  useEffect(() => {
    if (!id || paymentSuccess) return;

    const intervalId = setInterval(async () => {
      const success = await checkTransactionStatus();
      if (success) {
        clearInterval(intervalId);
      }
    }, 2000);

    // Cleanup function
    return () => {
      clearInterval(intervalId);
    };
  }, [id, paymentSuccess, checkTransactionStatus]);

  // Hiển thị loading
  if (isLoading) {
    return (
      <Container>
        <div className="py-6">
          <Card className="p-8">
            <div className="flex flex-col items-center justify-center min-h-[300px]">
              <div className="mb-4">
                <div className="relative">
                  <div className="h-16 w-16 rounded-full border-4 border-gray-200"></div>
                  <div className="absolute top-0 left-0 h-16 w-16 rounded-full border-t-4 border-primary animate-spin"></div>
                </div>
              </div>
              <Typography variant="h6" className="mb-2">
                {t('rpoint.payment.loading.title', 'Đang tải thông tin thanh toán')}
              </Typography>
              <Typography variant="body2" color="muted" className="text-center">
                {t('rpoint.payment.loading.description', 'Vui lòng đợi trong giây lát...')}
              </Typography>
            </div>
          </Card>
        </div>
      </Container>
    );
  }

  // Hiển thị lỗi
  if (error || !orderInfo) {
    return (
      <Container>
        <div className="py-6">
          <Card className="p-6 text-center">
            <Typography variant="h5" color="error" className="mb-4">
              {t('rpoint.payment.error', 'Đã có lỗi xảy ra')}
            </Typography>
            <Typography variant="body1" className="mb-6">
              {error instanceof Error
                ? error.message
                : error
                  ? String(error)
                  : t('rpoint.payment.notFound', 'Không tìm thấy thông tin đơn hàng')}
            </Typography>
            <Button variant="primary" onClick={() => navigate('/rpoint/packages')}>
              {t('rpoint.payment.backToPackages', 'Quay lại danh sách gói')}
            </Button>
          </Card>
        </div>
      </Container>
    );
  }

  // Hiển thị màn hình thành công khi thanh toán hoàn tất
  if (paymentSuccess) {
    return (
      <Container>
        <div className="py-6">
          <Card className="p-6 text-center">
            <div className="flex justify-center mb-4">
              <Icon name="check-circle" size="xl" className="text-success" />
            </div>
            <Typography variant="h4" className="mb-2">
              {t('rpoint.payment.success.title', 'Thanh toán thành công!')}
            </Typography>
            <Typography variant="body1" className="mb-6">
              {t(
                'rpoint.payment.success.description',
                'Bạn đã mua thành công {{points}} R-Point. Số R-Point đã được cộng vào tài khoản của bạn.',
                { points: orderInfo.pointsAmount.toLocaleString('vi-VN') }
              )}
            </Typography>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button variant="primary" onClick={() => navigate('/rpoint/packages')}>
                {t('rpoint.payment.success.backToPackages', 'Mua thêm R-Point')}
              </Button>
              <Button variant="outline" onClick={() => navigate('/profile')}>
                {t('rpoint.payment.success.viewProfile', 'Xem tài khoản')}
              </Button>
            </div>
          </Card>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <div className="py-6">
        {/* Tiêu đề trang */}
        <Typography variant="h4" className="font-bold mb-6">
          {t('rpoint.payment.title', 'Thanh toán đơn hàng')}
        </Typography>

        <div className="space-y-6">
          {/* Thông tin thanh toán */}
          <QRCodePayment
            qrCodeUrl={orderInfo.qrCodeUrl}
            amount={orderInfo.amount}
            transferContent={orderInfo.description}
            status={orderInfo.status === 'COMPLETED' ? 'COMPLETED' : 'PENDING'}
            bankInfo={{
              name: orderInfo.bankName,
              accountNumber: orderInfo.accountNumber,
              accountHolder: orderInfo.accountHolder,
              logo: orderInfo.logoPath || `/src/shared/assets/images/banks/${orderInfo.bankCode.toLowerCase()}.png`,
            }}
          />

          {/* Nút quay lại */}
          <div className="flex justify-center">
             <IconCard
              icon="arrow-left"
              onClick={() => navigate('/rpoint/packages')}
              variant="secondary"
              size="md"
              title={t('rpoint.payment.backToPackages', 'Quay lại danh sách gói')}
            />
          </div>
        </div>
      </div>
    </Container>
  );
};

export default RPointPaymentPage;
