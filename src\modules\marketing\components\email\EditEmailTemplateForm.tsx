import React, { useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import {
  FormItem,
  Input,
  Button,
  Select,
  Typography,
  Card,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import {
  useEmailTemplateAdapter,
  useUpdateEmailTemplateAdapter
} from '../../hooks/email/useEmailTemplatesAdapter';
import { EmailTemplateType, EmailVariable } from '../../types/email.types';

interface EditEmailTemplateFormProps {
  templateId: string;
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form chỉnh sửa Email Template theo quy tắc RedAI
 */
export function EditEmailTemplateForm({ templateId, onSuccess, onCancel }: EditEmailTemplateFormProps) {
  const { t } = useTranslation(['marketing', 'common']);
  const [previewMode, setPreviewMode] = useState<'design' | 'code'>('design');
  
  // Hooks
  const { data: templateData, isLoading: isLoadingTemplate } = useEmailTemplateAdapter(templateId);
  const updateTemplate = useUpdateEmailTemplateAdapter();
  const { setFormErrors } = useFormErrors<Record<string, string>>();

  // State cho form data
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    htmlContent: '',
    textContent: '',
    type: EmailTemplateType.NEWSLETTER,
    previewText: '',
    variables: [] as EmailVariable[],
    tags: [] as string[],
  });

  // Load dữ liệu template khi có templateData
  useEffect(() => {
    if (templateData) {
      setFormData({
        name: templateData.name || '',
        subject: templateData.subject || '',
        htmlContent: templateData.htmlContent || '',
        textContent: templateData.textContent || '',
        type: templateData.type || EmailTemplateType.NEWSLETTER,
        previewText: templateData.previewText || '',
        variables: templateData.variables || [],
        tags: templateData.tags || [],
      });
    }
  }, [templateData]);

  // Template types options
  const templateTypes = useMemo(
    () => [
      { value: EmailTemplateType.NEWSLETTER, label: t('marketing:email.templates.types.newsletter', 'Newsletter') },
      { value: EmailTemplateType.PROMOTIONAL, label: t('marketing:email.templates.types.promotional', 'Khuyến mãi') },
      { value: EmailTemplateType.TRANSACTIONAL, label: t('marketing:email.templates.types.transactional', 'Giao dịch') },
      { value: EmailTemplateType.WELCOME, label: t('marketing:email.templates.types.welcome', 'Chào mừng') },
      { value: EmailTemplateType.ABANDONED_CART, label: t('marketing:email.templates.types.abandonedCart', 'Giỏ hàng bỏ quên') },
      { value: EmailTemplateType.FOLLOW_UP, label: t('marketing:email.templates.types.followUp', 'Theo dõi') },
    ],
    [t]
  );

  // Xử lý thay đổi input
  const handleInputChange = (field: string, value: string | EmailTemplateType | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Xử lý submit form
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Validation
    const errors: Record<string, string> = {};
    if (!formData.name.trim()) {
      errors.name = t('marketing:email.templates.form.name.required', 'Tên template là bắt buộc');
    }
    if (!formData.subject.trim()) {
      errors.subject = t('marketing:email.templates.form.subject.required', 'Tiêu đề email là bắt buộc');
    }
    if (!formData.htmlContent.trim()) {
      errors.htmlContent = t('marketing:email.templates.form.htmlContent.required', 'Nội dung HTML là bắt buộc');
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      await updateTemplate.mutateAsync({
        id: templateId,
        data: {
          name: formData.name.trim(),
          subject: formData.subject.trim(),
          htmlContent: formData.htmlContent.trim(),
          tags: formData.tags,
          variables: formData.variables,
        },
      });

      onSuccess?.();
    } catch (error) {
      console.error('Error updating template:', error);
    }
  };

  // Xử lý hủy
  const handleCancel = () => {
    onCancel?.();
  };

  if (isLoadingTemplate) {
    return (
      <div className="p-6">
        <Typography variant="body1">Đang tải dữ liệu template...</Typography>
      </div>
    );
  }

  if (!templateData) {
    return (
      <div className="p-6">
        <Typography variant="body1">Không tìm thấy template</Typography>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Typography variant="h3">
          {t('marketing:email.templates.edit.title', 'Chỉnh sửa Email Template')}
        </Typography>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          {/* Basic Information */}
          <Card className="p-6">
            <Typography variant="h4" className="mb-4">
              {t('marketing:email.templates.form.basicInfo.title', 'Thông tin cơ bản')}
            </Typography>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItem
                label={t('marketing:email.templates.form.name.label', 'Tên Template')}
                name="name"
                required
              >
                <Input
                  fullWidth
                  placeholder={t('marketing:email.templates.form.name.placeholder', 'Ví dụ: Newsletter tháng 12')}
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
              </FormItem>

              <FormItem
                label={t('marketing:email.templates.form.type.label', 'Loại Template')}
                name="type"
                required
              >
                <Select
                  fullWidth
                  value={formData.type}
                  onChange={(value) => handleInputChange('type', value as EmailTemplateType)}
                  options={templateTypes}
                  placeholder={t('marketing:email.templates.form.type.placeholder', 'Chọn loại template')}
                />
              </FormItem>
            </div>

            <FormItem
              label={t('marketing:email.templates.form.subject.label', 'Tiêu đề Email')}
              name="subject"
              required
            >
              <Input
                fullWidth
                placeholder={t('marketing:email.templates.form.subject.placeholder', 'Ví dụ: 🎉 Khuyến mãi đặc biệt dành cho bạn!')}
                value={formData.subject}
                onChange={(e) => handleInputChange('subject', e.target.value)}
              />
            </FormItem>

            <FormItem
              label={t('marketing:email.templates.form.previewText.label', 'Preview Text')}
              name="previewText"
            >
              <Input
                fullWidth
                placeholder={t('marketing:email.templates.form.previewText.placeholder', 'Text hiển thị trong inbox preview')}
                value={formData.previewText}
                onChange={(e) => handleInputChange('previewText', e.target.value)}
              />
            </FormItem>
          </Card>

          {/* Content Section */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <Typography variant="h4">
                {t('marketing:email.templates.form.content.title', 'Nội dung Template')}
              </Typography>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant={previewMode === 'design' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setPreviewMode('design')}
                >
                  {t('marketing:email.templates.form.content.designMode', 'Design')}
                </Button>
                <Button
                  type="button"
                  variant={previewMode === 'code' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => setPreviewMode('code')}
                >
                  {t('marketing:email.templates.form.content.codeMode', 'HTML')}
                </Button>
              </div>
            </div>

            {previewMode === 'code' ? (
              <FormItem
                label={t('marketing:email.templates.form.htmlContent.label', 'HTML Content')}
                name="htmlContent"
                required
              >
                <textarea
                  className="w-full p-3 border border-border rounded-md min-h-[400px] font-mono text-sm bg-card-muted text-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary"
                  placeholder={t('marketing:email.templates.form.htmlContent.placeholder', '<!DOCTYPE html>\n<html>\n<head>\n  <title>Email Template</title>\n</head>\n<body>\n  <h1>Xin chào {customer_name}!</h1>\n  <p>Cảm ơn bạn đã đăng ký newsletter của chúng tôi.</p>\n</body>\n</html>')}
                  value={formData.htmlContent}
                  onChange={(e) => handleInputChange('htmlContent', e.target.value)}
                />
              </FormItem>
            ) : (
              <div className="border border-border rounded-md p-4 min-h-[400px] bg-card-muted">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('marketing:email.templates.form.content.designModeNote', 'Chế độ design sẽ được phát triển trong tương lai. Hiện tại vui lòng sử dụng chế độ HTML.')}
                </Typography>
              </div>
            )}
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3">
            <Button
              type="button"
              variant="secondary"
              onClick={handleCancel}
            >
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              type="submit"
              variant="primary"
              isLoading={updateTemplate.isPending}
            >
              {t('marketing:email.templates.edit.submit', 'Cập nhật Template')}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}

export default EditEmailTemplateForm;
