/**
 * Component thông tin xuất hóa đơn
 */
import React, { useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Radio, FormItem, FormGrid, Input, Select, Textarea, DatePickerFormField } from '@/shared/components/common';
import { useFormContext, useWatch } from 'react-hook-form';
import { InvoiceType } from '../schemas/order.schema';
import { useBusinessInfo, useCurrentUser } from '@/modules/profile';
import { GenderEnum } from '@/modules/profile/types/user.types';

interface InvoiceInfoProps {
  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component thông tin xuất hóa đơn
 */
const InvoiceInfo: React.FC<InvoiceInfoProps> = ({ className = '' }) => {
  const { t } = useTranslation(['rpoint']);
  const { register, control, setValue } = useFormContext();

  // Theo dõi loại hóa đơn
  const invoiceType = useWatch({
    control,
    name: 'invoiceInfo.type',
    defaultValue: InvoiceType.PERSONAL,
  });

  // Lấy thông tin doanh nghiệp từ API
  const { data: businessInfo } = useBusinessInfo();

  // Lấy thông tin người dùng từ API
  const { data: currentUser } = useCurrentUser();

  // Options cho giới tính
  const genderOptions = [
    { value: GenderEnum.MALE, label: t('rpoint.order.invoice.gender.male', 'Nam') },
    { value: GenderEnum.FEMALE, label: t('rpoint.order.invoice.gender.female', 'Nữ') },
    { value: GenderEnum.OTHER, label: t('rpoint.order.invoice.gender.other', 'Khác') },
  ];

  // Hàm điền thông tin doanh nghiệp vào form
  const fillBusinessInfo = useCallback(() => {
    if (businessInfo) {
      console.log('Filling business info:', businessInfo);

      // Điền thông tin doanh nghiệp vào form
      setValue('invoiceInfo.representativeName', businessInfo.representativeName || '');
      setValue('invoiceInfo.representativePosition', businessInfo.representativePosition || '');
      setValue('invoiceInfo.companyName', businessInfo.businessName || '');
      setValue('invoiceInfo.companyAddress', businessInfo.businessAddress || '');
      setValue('invoiceInfo.taxCode', businessInfo.taxCode || '');
      setValue('invoiceInfo.email', businessInfo.businessEmail || '');
    }
  }, [businessInfo, setValue]);

  // Hàm điền thông tin cá nhân vào form
  const fillPersonalInfo = useCallback(() => {
    if (currentUser) {
      setValue('invoiceInfo.fullName', currentUser.fullName || '');
      setValue('invoiceInfo.phoneNumber', currentUser.phoneNumber || '');
      setValue('invoiceInfo.address', currentUser.address || '');
      setValue('invoiceInfo.email', currentUser.email || '');
      setValue('invoiceInfo.dateOfBirth', currentUser.dateOfBirth || '');
      setValue('invoiceInfo.gender', currentUser.gender || '');
    }
  }, [currentUser, setValue]);

  // Xử lý khi thay đổi loại hóa đơn
  const handleInvoiceTypeChange = (type: InvoiceType) => {
    setValue('invoiceInfo.type', type);

    // Nếu chọn loại hóa đơn doanh nghiệp, điền thông tin doanh nghiệp
    if (type === InvoiceType.BUSINESS) {
      fillBusinessInfo();
    }
    // Nếu chọn loại hóa đơn cá nhân, điền thông tin cá nhân
    else if (type === InvoiceType.PERSONAL) {
      fillPersonalInfo();
    }
  };

  // Tự động điền thông tin khi component được tải
  useEffect(() => {
    if (invoiceType === InvoiceType.BUSINESS && businessInfo) {
      fillBusinessInfo();
    } else if (invoiceType === InvoiceType.PERSONAL && currentUser) {
      fillPersonalInfo();
    }
  }, [businessInfo, currentUser, invoiceType, fillBusinessInfo, fillPersonalInfo]);

  return (
    <Card
      title={t('rpoint.order.invoice.title', 'Thông tin xuất hóa đơn')}
      className={className}
      variant="bordered"
    >
      <div className="space-y-6">
        {/* Lựa chọn loại hóa đơn */}
        <div className="flex space-x-6">
          <div className="flex items-center space-x-2">
            <Radio
              checked={invoiceType === InvoiceType.PERSONAL}
              onChange={() => handleInvoiceTypeChange(InvoiceType.PERSONAL)}
              name="invoiceType"
              id="personal"
            />
            <label htmlFor="personal" className="cursor-pointer">
              <Typography variant="body2">
                {t('rpoint.order.invoice.personal', 'Cá nhân')}
              </Typography>
            </label>
          </div>
          <div className="flex items-center space-x-2">
            <Radio
              checked={invoiceType === InvoiceType.BUSINESS}
              onChange={() => handleInvoiceTypeChange(InvoiceType.BUSINESS)}
              name="invoiceType"
              id="business"
            />
            <label htmlFor="business" className="cursor-pointer">
              <Typography variant="body2">
                {t('rpoint.order.invoice.business', 'Doanh nghiệp')}
              </Typography>
            </label>
          </div>
        </div>

        {/* Thông tin doanh nghiệp (hiển thị khi chọn loại hóa đơn doanh nghiệp) */}
        {invoiceType === InvoiceType.BUSINESS && (
          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem
              name="invoiceInfo.representativeName"
              label={t('rpoint.order.invoice.representativeName', 'Người đại diện')}
              required
            >
              <Input
                {...register('invoiceInfo.representativeName')}
                placeholder={t(
                  'rpoint.order.invoice.representativeNamePlaceholder',
                  'Nhập tên người đại diện'
                )}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.representativePosition"
              label={t('rpoint.order.invoice.representativePosition', 'Vị trí người đại diện')}
              required
            >
              <Input
                {...register('invoiceInfo.representativePosition')}
                placeholder={t(
                  'rpoint.order.invoice.representativePositionPlaceholder',
                  'Nhập vị trí người đại diện'
                )}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.companyName"
              label={t('rpoint.order.invoice.companyName', 'Tên doanh nghiệp')}
              required
            >
              <Input
                {...register('invoiceInfo.companyName')}
                placeholder={t(
                  'rpoint.order.invoice.companyNamePlaceholder',
                  'Nhập tên doanh nghiệp'
                )}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.taxCode"
              label={t('rpoint.order.invoice.taxCode', 'Mã số thuế')}
              required
            >
              <Input
                {...register('invoiceInfo.taxCode')}
                placeholder={t('rpoint.order.invoice.taxCodePlaceholder', 'Nhập mã số thuế')}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.companyAddress"
              label={t('rpoint.order.invoice.companyAddress', 'Địa chỉ doanh nghiệp')}
              required
              className="col-span-2"
            >
              <Input
                {...register('invoiceInfo.companyAddress')}
                placeholder={t(
                  'rpoint.order.invoice.companyAddressPlaceholder',
                  'Nhập địa chỉ doanh nghiệp'
                )}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.email"
              label={t('rpoint.order.invoice.email', 'Email nhận thông báo hóa đơn')}
              required
              className="col-span-2"
            >
              <Input
                {...register('invoiceInfo.email')}
                placeholder={t(
                  'rpoint.order.invoice.emailPlaceholder',
                  'Nhập email nhận thông báo hóa đơn'
                )}
                fullWidth
              />
            </FormItem>
          </FormGrid>
        )}

        {/* Thông tin cá nhân (hiển thị khi chọn loại hóa đơn cá nhân) */}
        {invoiceType === InvoiceType.PERSONAL && (
          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <FormItem
              name="invoiceInfo.fullName"
              label={t('rpoint.order.invoice.fullName', 'Họ và tên')}
              required
            >
              <Input
                {...register('invoiceInfo.fullName')}
                placeholder={t(
                  'rpoint.order.invoice.fullNamePlaceholder',
                  'Nhập họ và tên'
                )}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.phoneNumber"
              label={t('rpoint.order.invoice.phoneNumber', 'Số điện thoại')}
              required
            >
              <Input
                {...register('invoiceInfo.phoneNumber')}
                placeholder={t(
                  'rpoint.order.invoice.phoneNumberPlaceholder',
                  'Nhập số điện thoại'
                )}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.email"
              label={t('rpoint.order.invoice.email', 'Email nhận thông báo hóa đơn')}
              required
              className="col-span-2"
            >
              <Input
                {...register('invoiceInfo.email')}
                placeholder={t(
                  'rpoint.order.invoice.emailPlaceholder',
                  'Nhập email nhận thông báo hóa đơn'
                )}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.address"
              label={t('rpoint.order.invoice.address', 'Địa chỉ')}
              required
              className="col-span-2"
            >
              <Textarea
                {...register('invoiceInfo.address')}
                placeholder={t(
                  'rpoint.order.invoice.addressPlaceholder',
                  'Nhập địa chỉ'
                )}
                rows={3}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.dateOfBirth"
              label={t('rpoint.order.invoice.dateOfBirth', 'Ngày sinh')}
            >
              <DatePickerFormField
                placeholder={t(
                  'rpoint.order.invoice.dateOfBirthPlaceholder',
                  'Chọn ngày sinh'
                )}
                format="dd/MM/yyyy"
                maxDate={new Date()}
                fullWidth
              />
            </FormItem>

            <FormItem
              name="invoiceInfo.gender"
              label={t('rpoint.order.invoice.gender', 'Giới tính')}
            >
              <Select
                name="invoiceInfo.gender"
                options={genderOptions}
                placeholder={t(
                  'rpoint.order.invoice.genderPlaceholder',
                  'Chọn giới tính'
                )}
                fullWidth
              />
            </FormItem>
          </FormGrid>
        )}
      </div>
    </Card>
  );
};

export default InvoiceInfo;
