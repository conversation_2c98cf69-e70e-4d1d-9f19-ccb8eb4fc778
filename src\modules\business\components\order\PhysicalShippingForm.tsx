import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  FormItem,
  Select,
  Input,
  Textarea,
} from '@/shared/components/common';
import { ShippingDto, ShippingMethod, AddressDto } from '../../types/order.types';

interface PhysicalShippingFormProps {
  shipping?: ShippingDto;
  onShippingChange: (shipping: ShippingDto) => void;
  customerAddress?: AddressDto;
}

/**
 * Form vận chuyển cho sản phẩm vật lý
 */
const PhysicalShippingForm: React.FC<PhysicalShippingFormProps> = ({
  shipping,
  onShippingChange,
  customerAddress,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Xử lý thay đổi phương thức vận chuyển
  const handleMethodChange = (method: ShippingMethod) => {
    onShippingChange({
      ...shipping,
      method,
      toAddress: customerAddress || {
        province: '',
        district: '',
        ward: '',
        address: '',
      },
    } as ShippingDto);
  };

  // Xử lý thay đổi phí vận chuyển
  const handleFeeChange = (fee: number) => {
    onShippingChange({
      ...shipping,
      fee,
    } as ShippingDto);
  };

  // Xử lý thay đổi ghi chú
  const handleNoteChange = (note: string) => {
    onShippingChange({
      ...shipping,
      note,
    } as ShippingDto);
  };

  // Xử lý thay đổi service
  const handleServiceChange = (serviceName: string) => {
    onShippingChange({
      ...shipping,
      serviceName,
    } as ShippingDto);
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h6" className="mb-4">
          {t('business:order.shippingInfo')}
        </Typography>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem label={t('business:order.shippingMethod')} required>
            <Select
              value={shipping?.method || ShippingMethod.SELF}
              onChange={(value) => handleMethodChange(value as ShippingMethod)}
              options={[
                { 
                  value: ShippingMethod.SELF, 
                  label: t('business:order.shipping.self') 
                },
                { 
                  value: ShippingMethod.GHN, 
                  label: t('business:order.shipping.ghn') 
                },
                { 
                  value: ShippingMethod.GHTK, 
                  label: t('business:order.shipping.ghtk') 
                },
              ]}
              fullWidth
            />
          </FormItem>
          
          <FormItem label={t('business:order.shippingFee')}>
            <Input
              type="number"
              value={shipping?.fee || 0}
              onChange={(e) => handleFeeChange(parseFloat(e.target.value) || 0)}
              placeholder="0"
              fullWidth
            />
          </FormItem>
        </div>

        {/* Service name cho GHN/GHTK */}
        {(shipping?.method === ShippingMethod.GHN || shipping?.method === ShippingMethod.GHTK) && (
          <FormItem label={t('business:order.shippingService')}>
            <Input
              value={shipping?.serviceName || ''}
              onChange={(e) => handleServiceChange(e.target.value)}
              placeholder={t('business:order.shippingServicePlaceholder')}
              fullWidth
            />
          </FormItem>
        )}
        
        <FormItem label={t('business:order.shippingNote')}>
          <Textarea
            value={shipping?.note || ''}
            onChange={(e) => handleNoteChange(e.target.value)}
            placeholder={t('business:order.shippingNotePlaceholder')}
            rows={3}
            fullWidth
          />
        </FormItem>

        {/* Hiển thị địa chỉ giao hàng */}
        {customerAddress && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <Typography variant="subtitle2" className="mb-2">
              {t('business:order.deliveryAddress')}
            </Typography>
            <Typography variant="body2" className="text-gray-600">
              {`${customerAddress.address}, ${customerAddress.ward}, ${customerAddress.district}, ${customerAddress.province}`}
            </Typography>
          </div>
        )}
      </div>
    </Card>
  );
};

export default PhysicalShippingForm;
