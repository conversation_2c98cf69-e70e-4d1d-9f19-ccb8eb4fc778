import React, { useRef } from 'react';
import { Typography, Icon } from './index';

interface AvatarImageUploaderProps {
  /**
   * URL ảnh hiện tại
   */
  value?: string;
  
  /**
   * Callback khi thay đổi ảnh
   */
  onChange: (imageUrl: string) => void;
  
  /**
   * Placeholder text
   */
  placeholder?: string;
  
  /**
   * Class bổ sung
   */
  className?: string;

  /**
   * Kích thước của avatar
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Hình dạng avatar
   */
  shape?: 'circle' | 'square';
}

/**
 * Component upload ảnh với giao diện avatar-style và các nút thay đổi/xóa inline
 */
const AvatarImageUploader: React.FC<AvatarImageUploaderProps> = ({
  value,
  onChange,
  placeholder = 'Chưa có hình ảnh',
  className = '',
  size = 'md',
  shape = 'square',
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Xử lý khi chọn file
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      
      if (!file.type.startsWith('image/')) {
        alert('Vui lòng chọn file ảnh');
        return;
      }

      // Tạo URL tạm thời cho preview
      const imageUrl = URL.createObjectURL(file);
      onChange(imageUrl);
    }
  };

  // Xử lý khi click nút thay đổi ảnh
  const handleChangeImage = () => {
    fileInputRef.current?.click();
  };

  // Xử lý khi click nút xóa ảnh
  const handleRemoveImage = () => {
    onChange('');
  };

  // Tính toán kích thước
  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
  };

  // Tính toán hình dạng
  const shapeClasses = {
    circle: 'rounded-full',
    square: 'rounded-lg',
  };

  return (
    <div className={`${className}`}>
      {/* Preview area with overlay buttons */}
      <div className="relative group">
        {value ? (
          <div className={`${sizeClasses[size]} ${shapeClasses[shape]} border border-gray-200 dark:border-gray-700 overflow-hidden bg-gray-50 dark:bg-gray-800 relative`}>
            <img
              src={value}
              alt="Preview"
              className="w-full h-full object-cover"
            />
            {/* Overlay with action buttons */}
            <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center space-x-1">
              <button
                onClick={handleChangeImage}
                className="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-1.5 transition-all duration-200 shadow-lg"
                title="Thay đổi hình ảnh"
              >
                <Icon name="upload" size="xs" className="text-gray-700" />
              </button>
              <button
                onClick={handleRemoveImage}
                className="bg-red-500 bg-opacity-90 hover:bg-opacity-100 rounded-full p-1.5 transition-all duration-200 shadow-lg"
                title="Xóa hình ảnh"
              >
                <Icon name="trash" size="xs" className="text-white" />
              </button>
            </div>
          </div>
        ) : (
          <div
            className={`${sizeClasses[size]} ${shapeClasses[shape]} border-2 border-dashed border-gray-300 dark:border-gray-600 flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-800 hover:border-primary hover:bg-primary/5 transition-colors cursor-pointer`}
            onClick={handleChangeImage}
          >
            <Icon name="upload" size="sm" className="mb-1 text-gray-400" />
            <Typography variant="caption" className="text-gray-500 dark:text-gray-400 text-center text-xs">
              {placeholder}
            </Typography>
          </div>
        )}
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

export default AvatarImageUploader;
