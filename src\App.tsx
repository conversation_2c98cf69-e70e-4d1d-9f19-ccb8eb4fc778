/**
 * <PERSON><PERSON><PERSON> bảo React được import đầu tiên
 */
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { HelmetProvider } from 'react-helmet-async';
import { ThemeProvider } from '@/shared/contexts/theme';
import { LanguageProvider } from '@/shared/contexts/language';
import { ChatPanelProvider } from '@/shared/contexts/chat-panel';
import { RPointProvider } from '@/shared/contexts/rpoint';
import { TaskQueueProvider } from '@/shared/contexts/TaskQueueContext';
import { store, persistor } from '@/shared/store';
import AppRouter from '@/shared/routers';

import TaskQueuePanelContainer from '@/shared/components/layout/task-queue-panel/TaskQueuePanelContainer';

// Import i18n (needs to be bundled)
import '@/lib/i18n';

// Tạo component nội bộ để khởi tạo auth sau khi Redux Persist đã khôi phục dữ liệu
const AppContent = () => {
  return (
    <HelmetProvider>
      <ThemeProvider>
        <LanguageProvider>
          <ChatPanelProvider>
            <RPointProvider>
              <TaskQueueProvider
                options={{ concurrency: 3, defaultMaxRetries: 3, autoRemoveCompletedAfter: 60000 }}
              >
                <AppRouter />
                <TaskQueuePanelContainer />
              </TaskQueueProvider>
            </RPointProvider>
          </ChatPanelProvider>
        </LanguageProvider>
      </ThemeProvider>
    </HelmetProvider>
  );
};

const App = () => {
  // HMR Test - Thay đổi comment này để test Hot Reload
  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <AppContent />
      </PersistGate>
    </Provider>
  );
};

export default App;
