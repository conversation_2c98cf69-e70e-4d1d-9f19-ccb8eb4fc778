/**
 * Schema cho form thông tin đơn hàng
 */
import { z } from 'zod';
import { ValidationSchemas } from '@/shared/validation/schemas';

/**
 * Enum cho loại thông tin xuất hóa đơn
 */
export enum InvoiceType {
  PERSONAL = 'personal',
  BUSINESS = 'business',
}

/**
 * Schema cho thông tin xuất hóa đơn cá nhân
 */
export const personalInvoiceSchema = z.object({
  type: z.literal(InvoiceType.PERSONAL),
  fullName: z.string().min(1, 'Vui lòng nhập họ và tên'),
  phoneNumber: z.string().min(1, '<PERSON>ui lòng nhập số điện thoại'),
  address: z.string().min(1, 'Vui lòng nhập địa chỉ'),
  email: z.string().email('Email không hợp lệ'),
  dateOfBirth: ValidationSchemas.flexibleDate().optional(),
  gender: z.string().optional(),
});

/**
 * Schema cho thông tin xuất hóa đơn doanh nghiệp
 */
export const businessInvoiceSchema = z.object({
  type: z.literal(InvoiceType.BUSINESS),
  representativeName: z.string().min(1, 'Vui lòng nhập tên người đại diện'),
  representativePosition: z.string().min(1, 'Vui lòng nhập vị trí người đại diện'),
  companyName: z.string().min(1, 'Vui lòng nhập tên doanh nghiệp'),
  companyAddress: z.string().min(1, 'Vui lòng nhập địa chỉ doanh nghiệp'),
  taxCode: z.string().min(10, 'Mã số thuế không hợp lệ').max(13, 'Mã số thuế không hợp lệ'),
  email: z.string().email('Email không hợp lệ'),
});

/**
 * Schema cho thông tin xuất hóa đơn (union type)
 */
export const invoiceInfoSchema = z.discriminatedUnion('type', [
  personalInvoiceSchema,
  businessInvoiceSchema,
]);

/**
 * Schema cho form đơn hàng
 */
export const orderFormSchema = z.object({
  couponCode: z.string().optional(),
  invoiceInfo: invoiceInfoSchema,
});

/**
 * Kiểu dữ liệu từ schema
 */
export type OrderFormValues = z.infer<typeof orderFormSchema>;
export type InvoiceInfoValues = z.infer<typeof invoiceInfoSchema>;
export type PersonalInvoiceValues = z.infer<typeof personalInvoiceSchema>;
export type BusinessInvoiceValues = z.infer<typeof businessInvoiceSchema>;
