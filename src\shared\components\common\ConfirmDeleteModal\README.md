# ConfirmDeleteModal Component

Modal xác nhận xóa dùng chung với hỗ trợ đa ngôn ngữ hoàn chỉnh.

## Tính năng

- ✅ Hỗ trợ đa ngôn ngữ (Tiếng V<PERSON>, English, 中文)
- ✅ Tự động tạo message phù hợp cho single/bulk delete
- ✅ Fallback values cho tất cả props
- ✅ Hỗ trợ interpolation cho itemName và itemCount
- ✅ Tuỳ chỉnh button text và variant

## Props

| Prop | Type | Default | Mô tả |
|------|------|---------|-------|
| `isOpen` | `boolean` | - | Trạng thái hiển thị modal |
| `onClose` | `() => void` | - | Callback khi đóng modal |
| `onConfirm` | `() => void` | - | Callback khi xác nhận xóa |
| `title` | `string` | `t('common:confirmDeleteTitle')` | Tiêu đề modal |
| `message` | `string` | Auto-generated | Nội dung thông báo |
| `itemName` | `string` | - | Tên item cần xóa |
| `itemCount` | `number` | - | Số lượng items (cho bulk delete) |
| `isSubmitting` | `boolean` | `false` | Trạng thái đang submit |
| `confirmButtonText` | `string` | `t('common:delete')` | Text nút xác nhận |
| `cancelButtonText` | `string` | `t('common:cancel')` | Text nút hủy |
| `confirmButtonVariant` | `string` | `'danger'` | Variant nút xác nhận |

## Translation Keys

Component sử dụng các translation keys sau từ namespace `common`:

```json
{
  "confirmDeleteTitle": "Xác nhận xóa",
  "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa mục này?",
  "confirmDeleteWithName": "Bạn có chắc chắn muốn xóa \"{{itemName}}\"?",
  "confirmBulkDeleteMessage": "Bạn có chắc chắn muốn xóa {{count}} mục đã chọn?",
  "cancel": "Hủy",
  "delete": "Xóa"
}
```

## Cách sử dụng

### 1. Basic Usage (Sử dụng translation mặc định)

```tsx
import { ConfirmDeleteModal } from '@/shared/components/common';

<ConfirmDeleteModal
  isOpen={showDeleteModal}
  onClose={() => setShowDeleteModal(false)}
  onConfirm={handleDelete}
/>
```

### 2. Single Item Delete với tên

```tsx
<ConfirmDeleteModal
  isOpen={showDeleteModal}
  onClose={() => setShowDeleteModal(false)}
  onConfirm={handleDelete}
  itemName="Sản phẩm ABC"
/>
```

### 3. Bulk Delete

```tsx
<ConfirmDeleteModal
  isOpen={showBulkDeleteModal}
  onClose={() => setShowBulkDeleteModal(false)}
  onConfirm={handleBulkDelete}
  itemCount={selectedItems.length}
/>
```

### 4. Custom Messages

```tsx
<ConfirmDeleteModal
  isOpen={showDeleteModal}
  onClose={() => setShowDeleteModal(false)}
  onConfirm={handleDelete}
  title={t('business:product.deleteTitle')}
  message={t('business:product.deleteWarning')}
/>
```

### 5. Custom Button Text

```tsx
<ConfirmDeleteModal
  isOpen={showDeleteModal}
  onClose={() => setShowDeleteModal(false)}
  onConfirm={handleDelete}
  confirmButtonText={t('common:remove')}
  confirmButtonVariant="warning"
/>
```

## Migration Guide

### Từ version cũ

**Trước:**
```tsx
<ConfirmDeleteModal
  isOpen={showDeleteModal}
  onClose={() => setShowDeleteModal(false)}
  onConfirm={handleDelete}
  title="Xác nhận xóa"
  message="Bạn có chắc chắn muốn xóa sản phẩm này?"
  itemName={product.name}
/>
```

**Sau:**
```tsx
<ConfirmDeleteModal
  isOpen={showDeleteModal}
  onClose={() => setShowDeleteModal(false)}
  onConfirm={handleDelete}
  itemName={product.name}
  // title và message sẽ tự động được tạo từ translation
/>
```

## Lưu ý

1. **Backward Compatibility**: Component vẫn hỗ trợ props `title` và `message` tùy chỉnh
2. **Auto Message Generation**: Nếu không truyền `message`, component sẽ tự động tạo message phù hợp
3. **Translation Namespace**: Sử dụng namespace `common` cho các translation keys chung
4. **Fallback Values**: Tất cả translation đều có fallback values bằng tiếng Việt
