# Test CustomerGeneralInfo Component

## Các lỗi đã sửa:

### 1. ✅ Lỗi đa ngôn ngữ
**Vấn đề**: Form labels hiển thị tiếng Anh (Full Name, Phone Number, Address, Customer Tags) dù đang ở mode tiếng Việt.

**Nguyên nhân**: Sau khi tối ưu file locales, các translation keys đã được chuyển từ `business:customer.form.*` sang `business:common.form.*`

**Giải pháp**:
- Cập nhật translation keys trong component:
  - `t('business:customer.form.name')` → `t('business:common.form.name')`
  - `t('business:customer.form.email')` → `t('business:common.form.email')`
  - `t('business:customer.form.phone')` → `t('business:common.form.phone')`
  - `t('business:customer.form.address')` → `t('business:common.form.address')`
  - `t('business:customer.form.tags')` → `t('business:common.form.tags')`

- Cập nhật validation schema để sử dụng đúng translation keys

### 2. ✅ Cải thiện chức năng upload avatar
**Vấn đề**: Upload avatar không hoạt động đúng cách.

**Cải thiện**:
- ✅ Thêm state `avatarFile` để lưu file đã chọn
- ✅ Hiển thị preview avatar ngay khi chọn file
- ✅ Thêm indicator (icon upload) khi đã chọn file mới
- ✅ Thêm nút xóa avatar
- ✅ Hiển thị tên file đã chọn
- ✅ Validation file type và size
- ✅ Sử dụng NotificationUtil thay vì alert
- ✅ Gửi avatar file info trong API call

## Cách test:

### Test 1: Kiểm tra đa ngôn ngữ
1. Mở trang `/business/customer`
2. Click vào một khách hàng để xem chi tiết
3. Kiểm tra form "Thông tin chung"
4. **Kết quả mong đợi**: Tất cả labels hiển thị tiếng Việt:
   - "Họ và tên" (không phải "Full Name")
   - "Email" 
   - "Số điện thoại" (không phải "Phone Number")
   - "Địa chỉ" (không phải "Address")
   - "Nhãn" (không phải "Customer Tags")

### Test 2: Kiểm tra upload avatar
1. Trong form "Thông tin chung"
2. Click nút "Thay đổi" dưới avatar
3. Chọn một file ảnh
4. **Kết quả mong đợi**:
   - Avatar preview thay đổi ngay lập tức
   - Hiển thị icon upload ở góc avatar
   - Hiển thị tên file đã chọn
   - Nút "Xóa" (trash icon) xuất hiện

### Test 3: Kiểm tra validation avatar
1. Click nút "Thay đổi"
2. Chọn file không phải ảnh (ví dụ: .txt, .pdf)
3. **Kết quả mong đợi**: Hiển thị notification lỗi "Vui lòng chọn file hình ảnh"

4. Chọn file ảnh > 5MB
5. **Kết quả mong đợi**: Hiển thị notification lỗi "Kích thước file không được vượt quá 5MB"

### Test 4: Kiểm tra API integration
1. Chỉnh sửa thông tin trong form
2. Click nút Save (icon check)
3. **Kết quả mong đợi**:
   - Loading state hiển thị
   - API call được gửi đến `PUT /user/convert-customers/{id}/basic-info`
   - Notification success hiển thị
   - Form data được cập nhật

### Test 5: Kiểm tra form validation
1. Xóa hết nội dung trường "Họ và tên"
2. Click Save
3. **Kết quả mong đợi**: Hiển thị lỗi validation trên field

4. Nhập email không hợp lệ
5. Click Save
6. **Kết quả mong đợi**: Hiển thị lỗi validation email

## API Format được gửi:

```json
PUT /user/convert-customers/1/basic-info
{
  "name": "Nguyễn Văn A",
  "phone": "0912345678",
  "email": {
    "primary": "<EMAIL>",
    "secondary": ""
  },
  "address": "123 Đường ABC, Quận 1, TP.HCM",
  "avatarFile": {
    "fileName": "avatar.jpg",
    "mimeType": "image/jpeg"
  }
}
```

## Các tính năng mới:

1. **Avatar Management**:
   - Upload preview ngay lập tức
   - Validation file type và size
   - Indicator khi có file mới
   - Nút xóa avatar
   - Hiển thị tên file

2. **Better UX**:
   - Loading states
   - Error notifications
   - Success notifications
   - Form validation với Zod

3. **API Integration**:
   - Đúng format như curl examples
   - Error handling
   - Success handling
   - Query invalidation

## Notes:
- Avatar file chỉ được gửi metadata (fileName, mimeType) trong API call
- Actual file upload cần được implement riêng nếu backend yêu cầu
- Form sử dụng controlled components với local state
- Validation sử dụng Zod schemas
